import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import apiService from '../../services/apiService';
import EnhancedLoadingScreen from '../UI/EnhancedLoadingScreen';
import RiasecRadarChart from './RiasecRadarChart';
import AssessmentExplanations from './AssessmentExplanations';

const ResultRiasec = () => {
  const { resultId } = useParams();
  const navigate = useNavigate();
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const fetchInProgressRef = useRef(false);
  const abortControllerRef = useRef(null);

  useEffect(() => {
    // Prevent duplicate calls
    if (fetchInProgressRef.current) {
      return;
    }

    const fetchResult = async (retryCount = 0) => {
      const maxRetries = 5;
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000); // Exponential backoff, max 10s

      // Create new AbortController for this fetch sequence
      abortControllerRef.current = new AbortController();

      try {
        fetchInProgressRef.current = true;
        const response = await apiService.getResultById(resultId);

        // Check if component is still mounted and request wasn't aborted
        if (!abortControllerRef.current?.signal.aborted) {
          if (response.success) {
            setResult(response.data);
            fetchInProgressRef.current = false;
          }
        }
      } catch (err) {
        // Check if the error is due to abort
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        // If it's a 404 and we haven't exceeded max retries, try again
        if (err.response?.status === 404 && retryCount < maxRetries) {
          setTimeout(() => {
            // Check if component is still mounted before retrying
            if (!abortControllerRef.current?.signal.aborted) {
              fetchResult(retryCount + 1);
            }
          }, retryDelay);
        } else {
          // Final error after all retries or non-404 error
          const errorMessage = retryCount >= maxRetries
            ? `Result not found after ${maxRetries + 1} attempts. The analysis may still be processing.`
            : err.response?.data?.message || 'Failed to load results';
          setError(errorMessage);
          fetchInProgressRef.current = false;
        }
      }
    };

    if (resultId) {
      fetchResult();
    } else {
      navigate('/dashboard');
    }

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      fetchInProgressRef.current = false;
    };
  }, [resultId, navigate]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRiasecInsights = (riasecData) => {
    if (!riasecData) return { top: [], bottom: [] };
    
    const entries = Object.entries(riasecData).sort(([,a], [,b]) => b - a);
    return {
      top: entries.slice(0, 3),
      bottom: entries.slice(-3)
    };
  };

  const getRiasecDescription = (type) => {
    const descriptions = {
      'realistic': 'Prefer hands-on, practical work with tools, machines, or animals. Value concrete results and physical activities.',
      'investigative': 'Enjoy analyzing, researching, and solving complex problems. Prefer intellectual challenges and scientific thinking.',
      'artistic': 'Value creativity, self-expression, and aesthetic experiences. Prefer unstructured environments and original work.',
      'social': 'Enjoy helping, teaching, and working with people. Value cooperation, understanding, and making a positive impact.',
      'enterprising': 'Like leading, persuading, and managing others. Value achievement, competition, and business success.',
      'conventional': 'Prefer organized, structured work with clear procedures. Value accuracy, efficiency, and systematic approaches.'
    };
    return descriptions[type] || 'A valuable career interest area';
  };

  const getRiasecCareers = (type) => {
    const careers = {
      'realistic': ['Engineer', 'Mechanic', 'Carpenter', 'Farmer', 'Pilot', 'Chef'],
      'investigative': ['Scientist', 'Researcher', 'Doctor', 'Analyst', 'Psychologist', 'Programmer'],
      'artistic': ['Designer', 'Writer', 'Musician', 'Actor', 'Photographer', 'Architect'],
      'social': ['Teacher', 'Counselor', 'Social Worker', 'Nurse', 'Therapist', 'Coach'],
      'enterprising': ['Manager', 'Sales Representative', 'Entrepreneur', 'Lawyer', 'Marketing Director', 'CEO'],
      'conventional': ['Accountant', 'Administrator', 'Banker', 'Secretary', 'Data Analyst', 'Librarian']
    };
    return careers[type] || [];
  };

  // Navigation cards data
  const navigationCards = [
    {
      title: 'Character Strengths',
      subtitle: 'VIA-IS Assessment',
      description: 'Explore your core character strengths and values',
      icon: '⭐',
      path: `/results/${resultId}/via-is`,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: 'Personality Traits',
      subtitle: 'OCEAN Assessment',
      description: 'Understand your personality dimensions',
      icon: '🧭',
      path: `/results/${resultId}/ocean`,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: 'Career Persona',
      subtitle: 'Integrated Profile',
      description: 'Your comprehensive career recommendations',
      icon: '🎪',
      path: `/results/${resultId}/persona`,
      color: 'from-indigo-500 to-indigo-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      {/* Main Content Area */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Loading State */}
        {!result && !error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <EnhancedLoadingScreen
              title="Loading RIASEC Results..."
              subtitle="Fetching your career interests analysis"
              skeletonCount={4}
              className="min-h-[600px]"
            />
          </motion.div>
        )}

        {/* Error State */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4 }}
            className="bg-red-50 border border-red-200 rounded-xl p-6 shadow-sm"
          >
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Unable to Load Results</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
                <div className="mt-4 space-x-3">
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-red-100 text-red-800 px-3 py-2 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
                  >
                    Retry
                  </button>
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="bg-gray-100 text-gray-800 px-3 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
                  >
                    Back to Overview
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Content State */}
        {result && (
          <>
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-10"
            >
              <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-6">
                <div className="flex-1">
                  <motion.h1
                    className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                  >
                    Career Interests
                    <span className="block text-2xl lg:text-3xl font-medium text-emerald-600 mt-2">
                      RIASEC Assessment
                    </span>
                  </motion.h1>
                  <motion.p
                    className="text-lg text-gray-600 max-w-2xl leading-relaxed"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    Discover your natural career interests and work environments that align with your personality.
                    The RIASEC model identifies six key interest areas that guide career satisfaction and success.
                  </motion.p>
                </div>

                <motion.div
                  className="flex flex-col sm:flex-row gap-3"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="px-6 py-3 bg-white border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium shadow-sm"
                  >
                    ← Back to Overview
                  </button>
                  <button
                    onClick={() => navigate('/dashboard')}
                    className="px-6 py-3 bg-emerald-600 text-white rounded-xl hover:bg-emerald-700 transition-all duration-200 font-medium shadow-lg"
                  >
                    Dashboard
                  </button>
                </motion.div>
              </div>

              <motion.div
                className="mt-8 p-6 bg-white rounded-2xl shadow-sm border border-gray-100"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div className="flex items-center space-x-6 text-sm text-gray-600">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                      </svg>
                      <span>Completed: {formatDate(result.created_at)}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full mr-2"></div>
                      <span className="font-medium">Career Interests Analysis</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <span className="px-3 py-1 bg-emerald-100 text-emerald-700 rounded-full font-medium">
                      🎯 RIASEC Model
                    </span>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Introduction Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="mb-12"
            >
              <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-8 border border-emerald-100">
                <div className="max-w-4xl">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    Understanding Your Career Interests
                  </h2>
                  <div className="grid md:grid-cols-2 gap-6 text-gray-700">
                    <div>
                      <p className="mb-4 leading-relaxed">
                        The RIASEC model, developed by psychologist John Holland, identifies six personality types
                        that correspond to different work environments and career paths. Your results show how
                        strongly you align with each type.
                      </p>
                      <p className="leading-relaxed">
                        Understanding these preferences helps you identify careers where you'll find greater
                        satisfaction, engagement, and success.
                      </p>
                    </div>
                    <div className="bg-white rounded-xl p-6 shadow-sm">
                      <h3 className="font-semibold text-gray-900 mb-3">The Six RIASEC Types:</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                          <span><strong>Realistic:</strong> Hands-on, practical work</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                          <span><strong>Investigative:</strong> Research and analysis</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-pink-500 rounded-full mr-3"></div>
                          <span><strong>Artistic:</strong> Creative expression</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                          <span><strong>Social:</strong> Helping and teaching</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
                          <span><strong>Enterprising:</strong> Leading and persuading</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-gray-500 rounded-full mr-3"></div>
                          <span><strong>Conventional:</strong> Organized, structured work</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* RIASEC Chart */}
            {result.assessment_data?.riasec && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className="mb-12"
              >
                <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">
                      Your Interest Profile
                    </h2>
                    <p className="text-gray-600">
                      Visual representation of your career interest strengths
                    </p>
                  </div>
                  <RiasecRadarChart data={result.assessment_data.riasec} />
                </div>
              </motion.div>
            )}

            {/* Career Interests Analysis */}
            {result.assessment_data?.riasec && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
                className="mb-12"
              >
                <div className="text-center mb-10">
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">
                    Your Career Interests Analysis
                  </h2>
                  <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                    Based on your responses, here's how you align with different career interest areas.
                    Your strongest interests indicate environments where you're likely to thrive.
                  </p>
                </div>

                <div className="space-y-8">
                  {/* Top Interests */}
                  <motion.div
                    className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl p-8 border border-emerald-100"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                  >
                    <div className="flex items-center mb-8">
                      <div className="w-12 h-12 bg-emerald-500 rounded-xl flex items-center justify-center mr-4">
                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">Your Strongest Interests</h3>
                        <p className="text-gray-600 mt-1">These areas represent your natural preferences and where you're likely to find the most satisfaction</p>
                      </div>
                    </div>
                    <div className="grid md:grid-cols-3 gap-6">
                      {getRiasecInsights(result.assessment_data.riasec).top.map(([type, score], index) => (
                        <motion.div
                          key={type}
                          className="bg-white rounded-xl p-6 shadow-sm border border-white/50"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: 0.9 + index * 0.1 }}
                          whileHover={{ y: -4, transition: { duration: 0.2 } }}
                        >
                          <div className="flex justify-between items-start mb-4">
                            <h4 className="text-xl font-bold text-gray-900 capitalize">
                              {type}
                            </h4>
                            <div className="text-right">
                              <div className="text-2xl font-bold text-emerald-600">{score.toFixed(1)}</div>
                              <div className="text-xs text-gray-500">out of 5.0</div>
                            </div>
                          </div>
                          <p className="text-gray-600 mb-4 leading-relaxed">{getRiasecDescription(type)}</p>
                          <div className="mb-4">
                            <div className="bg-gray-200 rounded-full h-3 overflow-hidden">
                              <motion.div
                                className="bg-gradient-to-r from-emerald-500 to-teal-500 h-3 rounded-full"
                                initial={{ width: 0 }}
                                animate={{ width: `${(score / 5) * 100}%` }}
                                transition={{ duration: 1, delay: 1 + index * 0.1 }}
                              />
                            </div>
                          </div>
                          <div>
                            <h5 className="text-sm font-semibold text-gray-700 mb-3">Related Career Paths:</h5>
                            <div className="flex flex-wrap gap-2">
                              {getRiasecCareers(type).slice(0, 4).map((career, careerIdx) => (
                                <span key={careerIdx} className="text-xs bg-emerald-100 text-emerald-700 px-3 py-1 rounded-full font-medium">
                                  {career}
                                </span>
                              ))}
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>

                  {/* Areas to Explore */}
                  <motion.div
                    className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 1.2 }}
                  >
                    <div className="flex items-center mb-8">
                      <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M9.504 1.132a1 1 0 01.992 0l1.75 1a1 1 0 11-.992 1.736L10 3.152l-1.254.716a1 1 0 11-.992-1.736l1.75-1zM5.618 4.504a1 1 0 01-.372 1.364L5.016 6l.23.132a1 1 0 11-.992 1.736L3 7.723V8a1 1 0 01-2 0V6a.996.996 0 01.52-.878l1.734-.99a1 1 0 011.364.372zm8.764 0a1 1 0 011.364-.372l1.734.99A.996.996 0 0118 6v2a1 1 0 11-2 0v-.277l-1.254.145a1 1 0 11-.992-1.736L14.984 6l-.23-.132a1 1 0 01-.372-1.364zm-7 4a1 1 0 011.364-.372L10 8.848l1.254-.716a1 1 0 11.992 1.736L11 10.723V12a1 1 0 11-2 0v-1.277l-1.246-.855a1 1 0 01-.372-1.364zM3 11a1 1 0 011 1v1.277l1.246.855a1 1 0 11-.992 1.736l-1.75-1A1 1 0 012 14v-2a1 1 0 011-1zm14 0a1 1 0 011 1v2a1 1 0 01-.504.868l-1.75 1a1 1 0 11-.992-1.736L16 13.277V12a1 1 0 011-1zm-9.618 5.504a1 1 0 011.364-.372l.254.145V16a1 1 0 112 0v.277l.254-.145a1 1 0 11.992 1.736l-1.735.992a.995.995 0 01-1.022 0l-1.735-.992a1 1 0 01-.372-1.364z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">Areas for Growth</h3>
                        <p className="text-gray-600 mt-1">These areas offer opportunities for exploration and skill development</p>
                      </div>
                    </div>
                    <div className="grid md:grid-cols-3 gap-6">
                      {getRiasecInsights(result.assessment_data.riasec).bottom.map(([type, score], index) => (
                        <motion.div
                          key={type}
                          className="bg-white rounded-xl p-6 shadow-sm border border-white/50"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: 1.3 + index * 0.1 }}
                          whileHover={{ y: -4, transition: { duration: 0.2 } }}
                        >
                          <div className="flex justify-between items-start mb-4">
                            <h4 className="text-xl font-bold text-gray-900 capitalize">
                              {type}
                            </h4>
                            <div className="text-right">
                              <div className="text-2xl font-bold text-blue-600">{score.toFixed(1)}</div>
                              <div className="text-xs text-gray-500">out of 5.0</div>
                            </div>
                          </div>
                          <p className="text-gray-600 mb-4 leading-relaxed">{getRiasecDescription(type)}</p>
                          <div className="mb-4">
                            <div className="bg-gray-200 rounded-full h-3 overflow-hidden">
                              <motion.div
                                className="bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full"
                                initial={{ width: 0 }}
                                animate={{ width: `${(score / 5) * 100}%` }}
                                transition={{ duration: 1, delay: 1.4 + index * 0.1 }}
                              />
                            </div>
                          </div>
                          <div>
                            <h5 className="text-sm font-semibold text-gray-700 mb-3">Explore These Careers:</h5>
                            <div className="flex flex-wrap gap-2">
                              {getRiasecCareers(type).slice(0, 4).map((career, careerIdx) => (
                                <span key={careerIdx} className="text-xs bg-blue-100 text-blue-700 px-3 py-1 rounded-full font-medium">
                                  {career}
                                </span>
                              ))}
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            )}

            {/* Navigation to Other Results */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.6 }}
              className="mb-12"
            >
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Explore Your Complete Profile
                </h2>
                <p className="text-gray-600">
                  Continue your journey by exploring other aspects of your assessment results
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-6">
                {navigationCards.map((card, index) => (
                  <motion.div
                    key={card.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 1.7 + index * 0.1 }}
                    whileHover={{
                      y: -8,
                      transition: { duration: 0.2 }
                    }}
                    className="group cursor-pointer"
                    onClick={() => navigate(card.path)}
                  >
                    <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg hover:border-gray-200 transition-all duration-300">
                      <div className="flex items-start justify-between mb-4">
                        <div className={`w-12 h-12 bg-gradient-to-r ${card.color} rounded-xl flex items-center justify-center text-white text-xl group-hover:scale-110 transition-transform duration-200`}>
                          {card.icon}
                        </div>
                        <motion.svg
                          className="w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          whileHover={{ x: 3 }}
                          transition={{ duration: 0.2 }}
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </motion.svg>
                      </div>

                      <div>
                        <h3 className="text-lg font-bold text-gray-900 mb-1 group-hover:text-gray-700 transition-colors">
                          {card.title}
                        </h3>
                        <p className="text-sm text-gray-500 mb-3 font-medium">
                          {card.subtitle}
                        </p>
                        <p className="text-gray-600 text-sm leading-relaxed">
                          {card.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Assessment Explanations */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 2.0 }}
            >
              <AssessmentExplanations showOnly="riasec" />
            </motion.div>
          </>
        )}
      </main>
    </div>
  );
};

export default ResultRiasec;
  